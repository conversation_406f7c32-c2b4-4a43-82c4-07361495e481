'use client'

import { useState } from 'react'
// import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, MapPin, Users, DollarSign, HelpCircle } from 'lucide-react'

import { PackageDetailsForm } from '../component/package-detail-form'
import { PackageStatusControls } from '../component/package-status-control'
import { CollapsibleSection } from '../component/collapsible'
import { AddItineraryForm } from '../component/add-itinerary-form'
import { ItineraryList } from '../component/itinerary-list'
import { TripHighlightsContent } from '../component/package-content'
import { PackageSidebar } from '../component/package-sidebar'

interface ItineraryItem {
  id: number;
  day: string;
  title: string;
  details: string;
  image?: string;
  heading: string;
  trekDistance: string;
  flightHours: string;
  drivingHour: string;
  highestAltitude: string;
  trekDuration: string;
}

// Initial default data
const defaultItems: ItineraryItem[] = [
  {
    id: 1,
    day: "1",
    title: "Drive from Pokhara-Ghandruk and Trek to Tadapani",
    details: "To reach Pokhara city from Kathmandu, you must take a 25-minute flight or a 7-hour jeep/bus drive.",
    image: '/images/random.jpeg',
    heading: 'Day 1 Heading',
    trekDistance: '5km',
    flightHours: '0',
    drivingHour: '3',
    highestAltitude: '1940m',
    trekDuration: '4 hours'
  },
  {
    id: 2,
    day: "2",
    title: "Tadapani to Dobato:",
    details: "After breakfast at the teahouse, we trek toward Dobato, walk through the forest, and climb steep trails.",
    image: '/images/random.jpeg',
    heading: 'Day 2 Heading',
    trekDistance: '6km',
    flightHours: '0',
    drivingHour: '0',
    highestAltitude: '3426m',
    trekDuration: '5 hours'
  },
];

export default function PackagePage() {
  const [activeTab, setActiveTab] = useState('itinerary');
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null); const [seoOpen, setSeoOpen] = useState(false)
  const [schemaOpen, setSchemaOpen] = useState(false)
  const [packageEditOpen, setPackageEditOpen] = useState(true)
  const [activeHighlight, setActiveHighlight] = useState('highlights')

  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>(defaultItems)



  const handleAddItinerary = (newItemData: Omit<ItineraryItem, 'id'>) => {
    const newItem = {
      ...newItemData,
      id: Date.now(), // Generate a unique ID
      image: newItemData.imageFile ? URL.createObjectURL(newItemData.imageFile) : undefined,
    };
    setItineraryItems((prevItems) => [...prevItems, newItem]);
  };

  // HANDLER: Sets the item to be edited in the form
  const handleEditItinerary = (id: number) => {
    const itemToEdit = itineraryItems.find((item) => item.id === id);
    if (itemToEdit) {
      setEditingItem(itemToEdit);
    }
  };

  // HANDLER: Updates an existing item in the list
  const handleUpdateItinerary = (updatedItemData: ItineraryItem) => {
    // Handle image update
    if (updatedItemData.imageFile) {
      updatedItemData.image = URL.createObjectURL(updatedItemData.imageFile);
    }

    setItineraryItems(itineraryItems.map((item) =>
      item.id === updatedItemData.id ? updatedItemData : item
    ));
    setEditingItem(null); // Exit editing mode
  };

  // HANDLER: Deletes an item from the list
  const handleDeleteItinerary = (id: number) => {
    setItineraryItems(items => items.filter(item => item.id !== id));
    if (editingItem && editingItem.id === id) {
      setEditingItem(null); // If the deleted item was being edited, exit edit mode
    }
  };

  // HANDLER: Cancels the edit and clears the form
  const handleCancelEdit = () => {
    setEditingItem(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Package</h1>
          {/* <Button className="bg-cyan-500 hover:bg-cyan-600">
            Add Package
          </Button> */}
        </div>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Package Details
            </TabsTrigger>
            <TabsTrigger value="itinerary" className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Itinerary
            </TabsTrigger>
            <TabsTrigger value="equipment" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Equipment
            </TabsTrigger>
            <TabsTrigger value="cost" className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Cost and Date
            </TabsTrigger>
            <TabsTrigger value="discount" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Group Discount Prices
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <HelpCircle className="w-4 h-4" />
              Package FAQ
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            {/* Package Status Controls */}
            <PackageStatusControls />



            {/* Collapsible Sections */}
            <div className="space-y-4">
              <CollapsibleSection
                title="SEO Details"
                isOpen={seoOpen}
                onToggle={() => setSeoOpen(!seoOpen)}
              >
                <p className="text-sm text-gray-600">SEO configuration options would go here...</p>
              </CollapsibleSection>

              <CollapsibleSection
                title="Schema Details"
                isOpen={schemaOpen}
                onToggle={() => setSchemaOpen(!schemaOpen)}
              >
                <p className="text-sm text-gray-600">Schema markup configuration would go here...</p>
              </CollapsibleSection>

              <CollapsibleSection
                title="Package Edit"
                isOpen={packageEditOpen}
                onToggle={() => setPackageEditOpen(!packageEditOpen)}
              >
                <PackageDetailsForm />
              </CollapsibleSection>
            </div>

            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-3">
                <PackageSidebar
                  activeHighlight={activeHighlight}
                  onHighlightChange={setActiveHighlight}
                />
              </div>
              <div className="col-span-9">
                <TripHighlightsContent activeHighlight={activeHighlight} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="itinerary" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AddItineraryForm
                editingItem={editingItem}
                onAddItinerary={handleAddItinerary}
                onUpdateItinerary={handleUpdateItinerary}
                onCancelEdit={handleCancelEdit}
              />
              <ItineraryList
                items={itineraryItems}
                onEdit={handleEditItinerary}
                onDelete={handleDeleteItinerary}
              />
            </div>
          </TabsContent>

          <TabsContent value="equipment">
            <Card>
              <CardHeader>
                <CardTitle>Equipment Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Equipment configuration options would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cost">
            <Card>
              <CardHeader>
                <CardTitle>Cost and Date Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Cost and date configuration options would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="discount">
            <Card>
              <CardHeader>
                <CardTitle>Group Discount Prices</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Group discount configuration options would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="faq">
            <Card>
              <CardHeader>
                <CardTitle>Package FAQ</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">FAQ management options would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
